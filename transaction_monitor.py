import requests
import time
import itertools
from datetime import datetime
from database import TransactionDB
from price_fetcher import PriceFetcher

class TransactionMonitor:
    def __init__(self, db: TransactionDB, price_fetcher: PriceFetcher):
        self.TOKEN_CONTRACT = "0x099Ea74Ed0a30f6Dcc5C7d5630eCE2aB5d147812"
        self.API_KEYS = ["X9GJN9WHPWVVYB3UIQYRZY199EDKZKX6Z7", "7CDM5BT6RDVY5ERN5RDXZ112SXSWNDCMQ1"]
        self.key_cycle = itertools.cycle(self.API_KEYS)
        
        self.db = db
        self.price_fetcher = price_fetcher
        self.monitoring = False
        
        # کال‌بک برای اطلاع‌رسانی به GUI
        self.on_new_transaction = None
        self.on_status_update = None
    
    def set_callbacks(self, on_new_transaction=None, on_status_update=None):
        """تنظیم کال‌بک‌ها برای اطلاع‌رسانی به GUI"""
        self.on_new_transaction = on_new_transaction
        self.on_status_update = on_status_update
    
    def get_kepi_transfers(self, api_key):
        """دریافت انتقالات KEPI از BSCScan API"""
        url = (
            "https://api.bscscan.com/api"
            "?module=account"
            "&action=tokentx"
            f"&contractaddress={self.TOKEN_CONTRACT}"
            "&page=1&offset=10&sort=desc"
            f"&apikey={api_key}"
        )
        
        try:
            response = requests.get(url, timeout=10)
            data = response.json()

            if data["status"] == "1":
                return data["result"]
            else:
                error_msg = f"⚠️ خطا در دریافت داده: {data.get('message', data.get('result', 'Unknown'))}"
                if self.on_status_update:
                    self.on_status_update(error_msg)
                return []
                
        except Exception as e:
            error_msg = f"❌ خطای اتصال: {e}"
            if self.on_status_update:
                self.on_status_update(error_msg)
            return []
    
    def process_transaction(self, tx):
        """پردازش و ذخیره تراکنش"""
        tx_hash = tx["hash"]
        
        # بررسی اینکه تراکنش قبلاً ذخیره شده یا نه
        if self.db.transaction_exists(tx_hash):
            return False
        
        # استخراج اطلاعات تراکنش
        from_addr = tx["from"]
        to_addr = tx["to"]
        token_amount = int(tx["value"]) / (10 ** int(tx["tokenDecimal"]))
        timestamp = int(tx["timeStamp"])
        date_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
        
        # دریافت قیمت فعلی
        current_price = self.price_fetcher.get_current_price()
        usd_value = None
        
        if current_price:
            usd_value = token_amount * current_price
        
        # آماده‌سازی داده‌ها برای ذخیره
        tx_data = {
            'tx_hash': tx_hash,
            'from_address': from_addr,
            'to_address': to_addr,
            'token_amount': token_amount,
            'usd_value': usd_value,
            'token_price': current_price,
            'timestamp': timestamp,
            'date_time': date_time,
            'block_number': tx.get("blockNumber"),
            'gas_used': tx.get("gasUsed"),
            'gas_price': tx.get("gasPrice")
        }
        
        # ذخیره در دیتابیس
        if self.db.insert_transaction(tx_data):
            # اطلاع‌رسانی به GUI
            if self.on_new_transaction:
                self.on_new_transaction(tx_data)
            
            # نمایش در کنسول
            usd_str = f" (${usd_value:.2f})" if usd_value else ""
            status_msg = f"📢 [{date_time}] KEPI انتقال {token_amount:,.2f} از {from_addr[:10]}... به {to_addr[:10]}...{usd_str}"
            
            if self.on_status_update:
                self.on_status_update(status_msg)
            
            return True
        
        return False
    
    def monitor_once(self):
        """یک دور مانیتورینگ"""
        current_key = next(self.key_cycle)
        transfers = self.get_kepi_transfers(current_key)
        
        new_count = 0
        for tx in transfers:
            if self.process_transaction(tx):
                new_count += 1
        
        if new_count == 0:
            status_msg = "🕒 بدون تراکنش جدید..."
            if self.on_status_update:
                self.on_status_update(status_msg)
        else:
            status_msg = f"✅ {new_count} تراکنش جدید پیدا شد"
            if self.on_status_update:
                self.on_status_update(status_msg)
        
        return new_count
    
    def start_monitoring(self):
        """شروع مانیتورینگ"""
        self.monitoring = True
        
        if self.on_status_update:
            self.on_status_update("🚀 مانیتورینگ شروع شد...")
    
    def stop_monitoring(self):
        """توقف مانیتورینگ"""
        self.monitoring = False
        
        if self.on_status_update:
            self.on_status_update("⏹️ مانیتورینگ متوقف شد")
    
    def monitor_loop(self):
        """حلقه اصلی مانیتورینگ"""
        while self.monitoring:
            try:
                self.monitor_once()
                
                # استراحت 5 ثانیه‌ای
                for _ in range(50):  # 5 ثانیه = 50 * 0.1 ثانیه
                    if not self.monitoring:
                        break
                    time.sleep(0.1)
                    
            except Exception as e:
                error_msg = f"❌ خطا در مانیتورینگ: {e}"
                if self.on_status_update:
                    self.on_status_update(error_msg)
                
                # استراحت بیشتر در صورت خطا
                time.sleep(10)
    
    def get_recent_transactions(self, limit=10):
        """دریافت آخرین تراکنش‌ها"""
        return self.db.get_all_transactions(limit=limit)
    
    def search_transactions(self, search_term):
        """جستجو در تراکنش‌ها"""
        return self.db.search_transactions(search_term)
    
    def get_statistics(self):
        """دریافت آمار کلی"""
        count = self.db.get_transaction_count()
        volume = self.db.get_total_volume()
        
        return {
            'total_transactions': count,
            'total_tokens': volume['total_tokens'],
            'total_usd': volume['total_usd']
        }
    
    def import_historical_data(self, pages=5):
        """واردات داده‌های تاریخی"""
        if self.on_status_update:
            self.on_status_update("📥 در حال واردات داده‌های تاریخی...")
        
        total_imported = 0
        
        for page in range(1, pages + 1):
            current_key = next(self.key_cycle)
            url = (
                "https://api.bscscan.com/api"
                "?module=account"
                "&action=tokentx"
                f"&contractaddress={self.TOKEN_CONTRACT}"
                f"&page={page}&offset=100&sort=desc"
                f"&apikey={current_key}"
            )
            
            try:
                response = requests.get(url, timeout=15)
                data = response.json()
                
                if data["status"] == "1":
                    page_imported = 0
                    for tx in data["result"]:
                        if self.process_transaction(tx):
                            page_imported += 1
                    
                    total_imported += page_imported
                    
                    if self.on_status_update:
                        self.on_status_update(f"📥 صفحه {page}: {page_imported} تراکنش جدید")
                    
                    # استراحت بین درخواست‌ها
                    time.sleep(1)
                else:
                    if self.on_status_update:
                        self.on_status_update(f"⚠️ خطا در صفحه {page}: {data.get('message', 'Unknown')}")
                    
            except Exception as e:
                if self.on_status_update:
                    self.on_status_update(f"❌ خطا در صفحه {page}: {e}")
        
        if self.on_status_update:
            self.on_status_update(f"✅ واردات تمام شد. {total_imported} تراکنش جدید اضافه شد")
        
        return total_imported
