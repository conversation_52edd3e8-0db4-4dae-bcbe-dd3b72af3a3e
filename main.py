#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KEPI Token Transaction Monitor
مانیتور تراکنش‌های توکن KEPI

این برنامه تراکنش‌های توکن KEPI را در شبکه BSC مانیتور می‌کند
و آن‌ها را در یک رابط گرافیکی نمایش می‌دهد.

نویسنده: AI Assistant
تاریخ: 2025
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# اضافه کردن مسیر فعلی به sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from gui import KEPIMonitorGUI
except ImportError as e:
    print(f"خطا در وارد کردن ماژول‌ها: {e}")
    print("لطفاً مطمئن شوید که همه فایل‌های مورد نیاز در همان پوشه قرار دارند.")
    sys.exit(1)

def main():
    """تابع اصلی برنامه"""
    try:
        # ایجاد پنجره اصلی
        root = tk.Tk()
        
        # تنظیم آیکون (اختیاری)
        try:
            # اگر فایل آیکون وجود داشت
            # root.iconbitmap('icon.ico')
            pass
        except:
            pass
        
        # تنظیم حداقل اندازه پنجره
        root.minsize(800, 600)
        
        # ایجاد GUI
        app = KEPIMonitorGUI(root)
        
        # نمایش پیام خوش‌آمدگویی
        welcome_msg = """
🚀 خوش آمدید به مانیتور تراکنش‌های KEPI!

ویژگی‌ها:
• مانیتورینگ real-time تراکنش‌ها
• نمایش قیمت فعلی و ارزش دلاری
• ذخیره در دیتابیس SQLite
• جستجو و مرتب‌سازی
• واردات داده‌های تاریخی

برای شروع، روی "Start Monitoring" کلیک کنید.
برای واردات داده‌های قبلی، روی "Import Historical" کلیک کنید.
        """
        
        app.add_status_message(welcome_msg)
        
        # شروع حلقه اصلی
        root.mainloop()
        
    except Exception as e:
        error_msg = f"خطا در اجرای برنامه: {e}"
        print(error_msg)
        
        # نمایش پیام خطا در صورت امکان
        try:
            messagebox.showerror("خطا", error_msg)
        except:
            pass
        
        sys.exit(1)

def check_dependencies():
    """بررسی وجود کتابخانه‌های مورد نیاز"""
    required_modules = ['requests', 'sqlite3', 'tkinter']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("کتابخانه‌های زیر یافت نشدند:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nلطفاً آن‌ها را نصب کنید:")
        print("pip install requests")
        return False
    
    return True

if __name__ == "__main__":
    print("🔍 بررسی وابستگی‌ها...")
    
    if not check_dependencies():
        print("❌ برخی وابستگی‌ها یافت نشدند.")
        sys.exit(1)
    
    print("✅ همه وابستگی‌ها موجود است.")
    print("🚀 شروع برنامه...")
    
    main()
