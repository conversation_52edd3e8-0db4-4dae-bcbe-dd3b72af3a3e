import requests
import time
from typing import Optional

class PriceFetcher:
    def __init__(self, api_key: Optional[str] = None):
        """
        ایجاد instance برای دریافت قیمت
        api_key: کلید API برای CoinMarketCap (اختیاری - برای محدودیت کمتر)
        """
        self.api_key = api_key
        self.base_url = "https://pro-api.coinmarketcap.com/v1"
        self.free_url = "https://api.coinmarketcap.com/v1"
        self.last_price = None
        self.last_update = 0
        self.cache_duration = 60  # کش قیمت برای 60 ثانیه
        
        # Headers برای API
        self.headers = {
            'Accept': 'application/json',
            'Accept-Encoding': 'deflate, gzip'
        }
        
        if self.api_key:
            self.headers['X-CMC_PRO_API_KEY'] = self.api_key
    
    def get_kepi_price_by_contract(self, contract_address: str = "******************************************") -> Optional[float]:
        """
        دریافت قیمت KEPI بر اساس آدرس کنترکت
        """
        # بررسی کش
        current_time = time.time()
        if self.last_price and (current_time - self.last_update) < self.cache_duration:
            return self.last_price
        
        try:
            if self.api_key:
                # استفاده از API پولی
                url = f"{self.base_url}/cryptocurrency/quotes/latest"
                params = {
                    'address': contract_address,
                    'convert': 'USD'
                }
            else:
                # استفاده از API رایگان - جستجو بر اساس نام
                return self.get_kepi_price_by_symbol()
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    # پردازش پاسخ API پولی
                    for token_data in data['data'].values():
                        if 'quote' in token_data and 'USD' in token_data['quote']:
                            price = token_data['quote']['USD']['price']
                            self.last_price = price
                            self.last_update = current_time
                            return price
                
            else:
                print(f"خطا در دریافت قیمت: {response.status_code}")
                
        except Exception as e:
            print(f"خطا در اتصال به CoinMarketCap: {e}")
        
        return None
    
    def get_kepi_price_by_symbol(self, symbol: str = "KEPI") -> Optional[float]:
        """
        دریافت قیمت KEPI بر اساس نماد (برای API رایگان)
        """
        try:
            # API رایگان CoinMarketCap (محدود)
            url = "https://api.coinmarketcap.com/v1/ticker/"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                for coin in data:
                    if coin.get('symbol', '').upper() == symbol.upper():
                        price = float(coin.get('price_usd', 0))
                        self.last_price = price
                        self.last_update = time.time()
                        return price
                        
        except Exception as e:
            print(f"خطا در دریافت قیمت از API رایگان: {e}")
        
        # اگر API رایگان کار نکرد، از CoinGecko استفاده کن
        return self.get_price_from_coingecko()
    
    def get_price_from_coingecko(self, token_id: str = "kepi") -> Optional[float]:
        """
        دریافت قیمت از CoinGecko (بک‌آپ)
        """
        try:
            # تلاش با ID های مختلف
            token_ids = ["kepi", "kepi-token", "kepi-finance"]

            for tid in token_ids:
                url = f"https://api.coingecko.com/api/v3/simple/price"
                params = {
                    'ids': tid,
                    'vs_currencies': 'usd'
                }

                response = requests.get(url, params=params, timeout=10)

                if response.status_code == 200:
                    data = response.json()

                    if tid in data and 'usd' in data[tid]:
                        price = data[tid]['usd']
                        self.last_price = price
                        self.last_update = time.time()
                        return price

        except Exception as e:
            print(f"خطا در دریافت قیمت از CoinGecko: {e}")

        # اگر توکن پیدا نشد، قیمت فرضی برگردان
        print("⚠️ توکن KEPI در CoinGecko پیدا نشد، از قیمت فرضی استفاده می‌شود")
        return 0.000001  # قیمت فرضی برای تست
    
    def get_price_from_pancakeswap(self, contract_address: str = "******************************************") -> Optional[float]:
        """
        دریافت قیمت از PancakeSwap (real-time)
        """
        try:
            # استفاده از PancakeSwap API
            url = f"https://api.pancakeswap.info/api/v2/tokens/{contract_address}"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'price' in data['data']:
                    price = float(data['data']['price'])
                    self.last_price = price
                    self.last_update = time.time()
                    return price
                    
        except Exception as e:
            print(f"خطا در دریافت قیمت از PancakeSwap: {e}")
        
        return None
    
    def get_current_price(self, contract_address: str = "******************************************") -> Optional[float]:
        """
        دریافت قیمت فعلی با تلاش از چندین منبع
        """
        # ابتدا از CoinMarketCap
        price = self.get_kepi_price_by_contract(contract_address)
        
        if price is None:
            # سپس از CoinGecko
            price = self.get_price_from_coingecko()
        
        if price is None:
            # در نهایت از PancakeSwap
            price = self.get_price_from_pancakeswap(contract_address)
        
        if price is None:
            print("⚠️ نتوانستم قیمت توکن را دریافت کنم، از قیمت فرضی استفاده می‌شود")
            # استفاده از قیمت فرضی برای تست
            price = 0.000001
            self.last_price = price
        
        return price
    
    def calculate_usd_value(self, token_amount: float, token_price: Optional[float] = None) -> Optional[float]:
        """
        محاسبه ارزش دلاری تراکنش
        """
        if token_price is None:
            token_price = self.get_current_price()
        
        if token_price is not None:
            return token_amount * token_price
        
        return None
