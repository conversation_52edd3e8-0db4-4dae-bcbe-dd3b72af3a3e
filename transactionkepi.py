import requests
import time
import itertools

TOKEN_CONTRACT = "0x099Ea74Ed0a30f6Dcc5C7d5630eCE2aB5d147812"
API_KEYS = ["X9GJN9WHPWVVYB3UIQYRZY199EDKZKX6Z7", "7CDM5BT6RDVY5ERN5RDXZ112SXSWNDCMQ1"]  # جایگزین کن با کلیدهای واقعی
key_cycle = itertools.cycle(API_KEYS)
seen_hashes = set()

def get_kepi_transfers(api_key):
    url = (
        "https://api.bscscan.com/api"
        "?module=account"
        "&action=tokentx"
        f"&contractaddress={TOKEN_CONTRACT}"
        "&page=1&offset=5&sort=desc"
        f"&apikey={api_key}"
    )
    try:
        response = requests.get(url, timeout=10)
        data = response.json()

        if data["status"] == "1":
            return data["result"]
        else:
            print("⚠️ خطا در دریافت داده:", data.get("message", data.get("result", "Unknown")))
            return []
    except Exception as e:
        print("❌ خطای اتصال:", e)
        return []

def monitor():
    while True:
        current_key = next(key_cycle)
        transfers = get_kepi_transfers(current_key)

        new_count = 0
        for tx in transfers:
            tx_hash = tx["hash"]
            if tx_hash not in seen_hashes:
                seen_hashes.add(tx_hash)

                from_addr = tx["from"]
                to_addr = tx["to"]
                value = int(tx["value"]) / (10 ** int(tx["tokenDecimal"]))
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(tx["timeStamp"])))
                print(f"📢 [{time_str}] KEPI انتقال {value} از {from_addr} به {to_addr}")
                new_count += 1

        if new_count == 0:
            print("🕒 بدون تراکنش جدید...")

        time.sleep(5)  # زمان بیشتر تا محدودیت نخوریم

if __name__ == "__main__":
    monitor()
