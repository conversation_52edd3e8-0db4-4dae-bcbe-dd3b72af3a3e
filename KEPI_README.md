# KEPI Token Transaction Monitor

مانیتور تراکنش‌های توکن KEPI در شبکه Binance Smart Chain (BSC) با رابط گرافیکی

## ویژگی‌ها

### 🔍 مانیتورینگ Real-time
- مانیتورینگ مداوم تراکنش‌های توکن KEPI
- نمایش فوری تراکنش‌های جدید
- استفاده از BSCScan API

### 💰 قیمت‌گذاری
- دریافت قیمت فعلی از CoinMarketCap
- محاسبه ارزش دلاری هر تراکنش
- پشتیبانی از چندین منبع قیمت (CoinGecko، PancakeSwap)

### 🗄️ ذخیره‌سازی
- ذخیره تمام تراکنش‌ها در دیتابیس SQLite
- عدم تکرار تراکنش‌ها
- ایندکس‌گذاری برای جستجوی سریع

### 🔎 جستجو و مرتب‌سازی
- جستجو در آدرس‌ها و hash تراکنش‌ها
- مرتب‌سازی بر اساس هر ستون
- فیلتر کردن نتایج

### 📊 آمار و گزارش
- نمایش تعداد کل تراکنش‌ها
- حجم کل انتقالات
- ارزش کل دلاری

## نصب و راه‌اندازی

### پیش‌نیازها
```bash
pip install requests
```

### اجرای برنامه
```bash
python main.py
```

## ساختار فایل‌ها

```
├── main.py                 # فایل اصلی برنامه
├── gui.py                  # رابط گرافیکی
├── database.py             # مدیریت دیتابیس SQLite
├── price_fetcher.py        # دریافت قیمت از API های مختلف
├── transaction_monitor.py  # منطق مانیتورینگ تراکنش‌ها
├── transactionkepi.py     # فایل اصلی قدیمی (مرجع)
└── kepi_transactions.db   # فایل دیتابیس (ایجاد خودکار)
```

## نحوه استفاده

### 1. شروع مانیتورینگ
- روی دکمه "Start Monitoring" کلیک کنید
- برنامه شروع به بررسی تراکنش‌های جدید می‌کند

### 2. واردات داده‌های تاریخی
- روی دکمه "Import Historical" کلیک کنید
- برنامه تراکنش‌های قبلی را دریافت و ذخیره می‌کند

### 3. جستجو
- در کادر "Search" متن مورد نظر را تایپ کنید
- نتایج به صورت فوری فیلتر می‌شوند

### 4. مرتب‌سازی
- روی عنوان هر ستون کلیک کنید
- ترتیب صعودی/نزولی تغییر می‌کند

## تنظیمات

### API Keys
در فایل `transaction_monitor.py` می‌توانید کلیدهای API خود را تغییر دهید:

```python
self.API_KEYS = ["YOUR_BSCSCAN_API_KEY_1", "YOUR_BSCSCAN_API_KEY_2"]
```

### آدرس کنترکت
آدرس کنترکت توکن KEPI:
```
0x099Ea74Ed0a30f6Dcc5C7d5630eCE2aB5d147812
```

### منابع قیمت
برنامه از سه منبع برای دریافت قیمت استفاده می‌کند:
1. CoinMarketCap (اولویت اول)
2. CoinGecko (پشتیبان)
3. PancakeSwap (real-time)

## عیب‌یابی

### مشکلات رایج

#### خطای "Rate Limit"
- از چندین کلید API استفاده کنید
- زمان انتظار بین درخواست‌ها را افزایش دهید

#### قیمت نمایش داده نمی‌شود
- اتصال اینترنت را بررسی کنید
- کلید API CoinMarketCap را تنظیم کنید

#### تراکنش‌ها نمایش داده نمی‌شوند
- کلیدهای BSCScan API را بررسی کنید
- فایروال یا VPN را بررسی کنید

## توسعه

### اضافه کردن ویژگی جدید
1. منطق را در ماژول مناسب پیاده‌سازی کنید
2. رابط کاربری را در `gui.py` بروزرسانی کنید
3. اتصالات لازم را برقرار کنید

### تست
```bash
python -m pytest tests/  # اگر تست‌ها اضافه شوند
```

## مجوز
این پروژه تحت مجوز MIT منتشر شده است.

## پشتیبانی
برای گزارش باگ یا درخواست ویژگی جدید، لطفاً issue ایجاد کنید.
