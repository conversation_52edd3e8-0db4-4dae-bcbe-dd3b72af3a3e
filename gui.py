import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
from database import TransactionDB
from price_fetcher import PriceFetcher
from transaction_monitor import TransactionMonitor

class KEPIMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("KEPI Token Monitor")
        self.root.geometry("1200x700")
        
        # تنظیم فونت فارسی
        self.setup_fonts()
        
        # ایجاد اتصال به دیتابیس و price fetcher
        self.db = TransactionDB()
        self.price_fetcher = PriceFetcher()
        self.monitor = TransactionMonitor(self.db, self.price_fetcher)

        # تنظیم کال‌بک‌ها
        self.monitor.set_callbacks(
            on_new_transaction=self.on_new_transaction,
            on_status_update=self.on_status_update
        )

        # متغیرهای کنترل
        self.monitoring = False
        self.monitor_thread = None
        self.current_price = None
        
        # ایجاد رابط کاربری
        self.create_widgets()
        
        # بارگذاری تراکنش‌های موجود
        self.load_transactions()
        
        # دریافت قیمت اولیه
        self.update_price()
    
    def setup_fonts(self):
        """تنظیم فونت‌ها"""
        self.default_font = ("Arial", 9)
        self.header_font = ("Arial", 10, "bold")
        self.title_font = ("Arial", 12, "bold")
    
    def create_widgets(self):
        """ایجاد ویجت‌های رابط کاربری"""
        # تنظیم grid weights برای پنجره اصلی
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # فریم اصلی
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تنظیم grid weights برای فریم اصلی
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)  # جدول اصلی

        # ردیف 0: عنوان
        title_label = ttk.Label(main_frame, text="KEPI Token Transaction Monitor",
                               font=self.title_font)
        title_label.grid(row=0, column=0, pady=(0, 15), sticky=tk.W+tk.E)

        # ردیف 1: فریم کنترل‌ها
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)

        # ردیف اول کنترل‌ها: دکمه‌ها
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.monitor_btn = ttk.Button(buttons_frame, text="Start Monitoring",
                                     command=self.toggle_monitoring, width=15)
        self.monitor_btn.grid(row=0, column=0, padx=(0, 10))

        ttk.Button(buttons_frame, text="Update Price",
                  command=self.update_price, width=12).grid(row=0, column=1, padx=(0, 10))

        ttk.Button(buttons_frame, text="Import Historical",
                  command=self.import_historical, width=15).grid(row=0, column=2, padx=(0, 10))

        ttk.Button(buttons_frame, text="Clear Search",
                  command=self.clear_search, width=12).grid(row=0, column=3)

        # ردیف دوم کنترل‌ها: قیمت
        price_frame = ttk.Frame(control_frame)
        price_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))

        ttk.Label(price_frame, text="Current Price:", font=self.header_font).grid(row=0, column=0, padx=(0, 10))
        self.price_label = ttk.Label(price_frame, text="Loading...",
                                    font=self.header_font, foreground="green")
        self.price_label.grid(row=0, column=1)
        
        # ردیف 2: فریم جستجو
        search_frame = ttk.LabelFrame(main_frame, text="Search", padding="10")
        search_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)

        ttk.Label(search_frame, text="Search:", font=self.default_font).grid(row=0, column=0, padx=(0, 10))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, font=self.default_font)
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        # فریم جدول
        table_frame = ttk.LabelFrame(main_frame, text="Transactions", padding="5")
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        
        # ایجاد Treeview برای جدول
        columns = ('Hash', 'From', 'To', 'Amount', 'USD Value', 'Price', 'Date/Time')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تنظیم ستون‌ها
        self.tree.heading('Hash', text='Transaction Hash', command=lambda: self.sort_column('Hash'))
        self.tree.heading('From', text='From Address', command=lambda: self.sort_column('From'))
        self.tree.heading('To', text='To Address', command=lambda: self.sort_column('To'))
        self.tree.heading('Amount', text='KEPI Amount', command=lambda: self.sort_column('Amount'))
        self.tree.heading('USD Value', text='USD Value', command=lambda: self.sort_column('USD Value'))
        self.tree.heading('Price', text='Token Price', command=lambda: self.sort_column('Price'))
        self.tree.heading('Date/Time', text='Date/Time', command=lambda: self.sort_column('Date/Time'))
        
        # تنظیم عرض ستون‌ها
        self.tree.column('Hash', width=120, minwidth=100)
        self.tree.column('From', width=120, minwidth=100)
        self.tree.column('To', width=120, minwidth=100)
        self.tree.column('Amount', width=100, minwidth=80)
        self.tree.column('USD Value', width=100, minwidth=80)
        self.tree.column('Price', width=100, minwidth=80)
        self.tree.column('Date/Time', width=150, minwidth=120)
        
        # اسکرول بار
        scrollbar_v = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # قرار دادن ویجت‌ها
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # فریم آمار
        stats_frame = ttk.LabelFrame(main_frame, text="Statistics", padding="5")
        stats_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="Total Transactions: 0 | Total Volume: 0 KEPI | Total USD: $0")
        self.stats_label.grid(row=0, column=0)

        # فریم وضعیت
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="5")
        status_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)

        # نوار وضعیت
        self.status_text = scrolledtext.ScrolledText(status_frame, height=4, wrap=tk.WORD)
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.status_text.config(state=tk.DISABLED)
        
        # متغیر برای مرتب‌سازی
        self.sort_reverse = False
        self.last_sort_column = None
    
    def update_price(self):
        """بروزرسانی قیمت توکن"""
        def fetch_price():
            price = self.price_fetcher.get_current_price()
            if price:
                self.current_price = price
                self.root.after(0, lambda: self.price_label.config(text=f"${price:.8f}"))
            else:
                self.root.after(0, lambda: self.price_label.config(text="Price unavailable"))
        
        threading.Thread(target=fetch_price, daemon=True).start()
    
    def toggle_monitoring(self):
        """شروع/توقف مانیتورینگ"""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()
    
    def start_monitoring(self):
        """شروع مانیتورینگ"""
        self.monitoring = True
        self.monitor_btn.config(text="Stop Monitoring", state="normal")
        
        # شروع thread مانیتورینگ
        self.monitor_thread = threading.Thread(target=self.monitor_transactions, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """توقف مانیتورینگ"""
        self.monitoring = False
        self.monitor_btn.config(text="Start Monitoring")
    
    def monitor_transactions(self):
        """مانیتورینگ تراکنش‌ها (در thread جداگانه)"""
        self.monitor.start_monitoring()
        self.monitor.monitor_loop()
    
    def load_transactions(self):
        """بارگذاری تراکنش‌ها از دیتابیس"""
        transactions = self.db.get_all_transactions(limit=1000)
        
        # پاک کردن جدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # اضافه کردن تراکنش‌ها
        for tx in transactions:
            self.add_transaction_to_tree(tx)
        
        # بروزرسانی آمار
        self.update_statistics()
    
    def add_transaction_to_tree(self, tx_data):
        """اضافه کردن تراکنش به جدول"""
        # فرمت کردن داده‌ها
        hash_short = tx_data[0][:10] + "..." if len(tx_data[0]) > 10 else tx_data[0]
        from_short = tx_data[1][:10] + "..." if len(tx_data[1]) > 10 else tx_data[1]
        to_short = tx_data[2][:10] + "..." if len(tx_data[2]) > 10 else tx_data[2]
        amount = f"{tx_data[3]:,.2f}"
        usd_value = f"${tx_data[4]:,.2f}" if tx_data[4] else "N/A"
        price = f"${tx_data[5]:.8f}" if tx_data[5] else "N/A"
        date_time = tx_data[7]
        
        self.tree.insert('', 0, values=(hash_short, from_short, to_short, amount, usd_value, price, date_time))
    
    def on_search(self, event=None):
        """جستجو در تراکنش‌ها"""
        search_term = self.search_var.get().strip()
        
        if search_term:
            transactions = self.db.search_transactions(search_term)
        else:
            transactions = self.db.get_all_transactions(limit=1000)
        
        # پاک کردن جدول
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # اضافه کردن نتایج جستجو
        for tx in transactions:
            self.add_transaction_to_tree(tx)
    
    def clear_search(self):
        """پاک کردن جستجو"""
        self.search_var.set("")
        self.load_transactions()
    
    def sort_column(self, column):
        """مرتب‌سازی ستون"""
        # تغییر جهت مرتب‌سازی
        if self.last_sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_reverse = False
        
        self.last_sort_column = column
        
        # دریافت داده‌ها و مرتب‌سازی
        items = [(self.tree.set(child, column), child) for child in self.tree.get_children('')]
        
        # مرتب‌سازی بر اساس نوع داده
        if column in ['Amount', 'USD Value', 'Price']:
            # مرتب‌سازی عددی
            items.sort(key=lambda x: float(x[0].replace('$', '').replace(',', '').replace('N/A', '0')), 
                      reverse=self.sort_reverse)
        else:
            # مرتب‌سازی متنی
            items.sort(reverse=self.sort_reverse)
        
        # مرتب کردن آیتم‌ها در tree
        for index, (val, child) in enumerate(items):
            self.tree.move(child, '', index)
    
    def update_statistics(self):
        """بروزرسانی آمار"""
        count = self.db.get_transaction_count()
        volume = self.db.get_total_volume()
        
        stats_text = f"Total Transactions: {count:,} | Total Volume: {volume['total_tokens']:,.2f} KEPI | Total USD: ${volume['total_usd']:,.2f}"
        self.stats_label.config(text=stats_text)

    def on_new_transaction(self, tx_data):
        """کال‌بک برای تراکنش جدید"""
        # اضافه کردن به جدول در main thread
        self.root.after(0, lambda: self.add_new_transaction_to_tree(tx_data))
        # بروزرسانی آمار
        self.root.after(0, self.update_statistics)

    def on_status_update(self, message):
        """کال‌بک برای بروزرسانی وضعیت"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"

        # اضافه کردن به نوار وضعیت در main thread
        self.root.after(0, lambda: self.add_status_message(full_message))

    def add_status_message(self, message):
        """اضافه کردن پیام به نوار وضعیت"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, message)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)

        # محدود کردن تعداد خطوط
        lines = self.status_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete("1.0", "10.0")
            self.status_text.config(state=tk.DISABLED)

    def add_new_transaction_to_tree(self, tx_data):
        """اضافه کردن تراکنش جدید به ابتدای جدول"""
        # فرمت کردن داده‌ها
        hash_short = tx_data['tx_hash'][:10] + "..." if len(tx_data['tx_hash']) > 10 else tx_data['tx_hash']
        from_short = tx_data['from_address'][:10] + "..." if len(tx_data['from_address']) > 10 else tx_data['from_address']
        to_short = tx_data['to_address'][:10] + "..." if len(tx_data['to_address']) > 10 else tx_data['to_address']
        amount = f"{tx_data['token_amount']:,.2f}"
        usd_value = f"${tx_data['usd_value']:,.2f}" if tx_data['usd_value'] else "N/A"
        price = f"${tx_data['token_price']:.8f}" if tx_data['token_price'] else "N/A"
        date_time = tx_data['date_time']

        # اضافه کردن به ابتدای جدول
        item = self.tree.insert('', 0, values=(hash_short, from_short, to_short, amount, usd_value, price, date_time))

        # هایلایت کردن تراکنش جدید
        self.tree.set(item, 'Hash', f"🆕 {hash_short}")

        # حذف هایلایت بعد از 5 ثانیه
        self.root.after(5000, lambda: self.tree.set(item, 'Hash', hash_short))

    def import_historical(self):
        """واردات داده‌های تاریخی"""
        def import_thread():
            self.monitor.import_historical_data(pages=3)
            # بروزرسانی جدول بعد از واردات
            self.root.after(0, self.load_transactions)

        threading.Thread(target=import_thread, daemon=True).start()
